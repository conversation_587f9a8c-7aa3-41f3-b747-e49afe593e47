const { defineConfig } = require('@vue/cli-service')
const path = require('path')


const name = '大屏报表' // 网页标题
function resolve(dir) {
  return path.join(__dirname, dir)
}
module.exports = defineConfig({
  transpileDependencies: true,
  publicPath: '/customer_report/',
  devServer: {
    proxy: {
      '/vue-api': {
        // target: 'https://ch-smf.linkskycloud.com/vue-api', // 后端API服务地址
        target: 'http://localhost:3000', // 后端API服务地址
        changeOrigin: true, // 允许跨域请求
        pathRewrite: {
          '^/vue-api': '' // 将请求路径中的/vue-api/前缀替换为/
        }
      }
    }
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        '@': resolve('src')
      }
    },
    plugins: [
    ],
  },
})
