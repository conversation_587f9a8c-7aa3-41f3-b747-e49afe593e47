{"name": "odoo-report-forms", "version": "0.1.0", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build:prod": "vue-cli-service build --mode prod", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"@jiaminghi/data-view": "^2.10.0", "axios": "0.28.1", "core-js": "^3.8.3", "element-ui": "^2.15.14", "vue": "^2.6.14", "vue-router": "3.4.9", "vue-seamless-scroll": "^1.1.23"}, "devDependencies": {"@babel/core": "^7.12.16", "@babel/eslint-parser": "^7.12.16", "@vue/cli-plugin-babel": "~5.0.0", "@vue/cli-plugin-eslint": "~5.0.0", "@vue/cli-service": "~5.0.0", "babel-eslint": "10.1.0", "babel-plugin-dynamic-import-node": "2.3.3", "eslint": "^7.32.0", "eslint-plugin-vue": "^8.0.3", "sass": "1.32.13", "sass-loader": "10.1.1", "vue-template-compiler": "^2.6.14"}, "eslintConfig": {"root": false, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "@babel/eslint-parser"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "volta": {"node": "18.20.7"}}