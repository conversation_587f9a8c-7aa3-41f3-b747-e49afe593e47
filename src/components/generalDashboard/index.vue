<template>
  <report-layout :report-title="reportTitle">
    <div class="table-container">
      <el-table
        ref="elTable"
        size="large"
        :header-cell-style="{ fontSize: '16px' }"
        :cell-style="{ fontSize: '16px' }"
        :data="visibleData"
        style="width: 100%"
        :max-height="tableMaxHeight"
        :row-class-name="getRowClassName"
      >
        <el-table-column
          v-for="column in tableColumns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :min-width="column.minWidth"
          :align="column.align"
        />
      </el-table>
    </div>
  </report-layout>
</template>

<script>
import ReportLayout from "@/components/layout/reportLayout.vue";
import axios from "axios";
import commonConst, { STARE } from "@/api/common/commonConst";

export default {
  name: "GeneralDashboard",
  components: {
    ReportLayout,
  },
  props: {
    reportTitle: {
      type: String,
      default: "通用看板",
    },
    apiConfig: {
      type: Object,
      default: () => ({
        url: "",
        pageSize: 20,
        mockDataTotal: 100,
        isMock: false,
        mockConfig: [
          {
            type: "string",
            prefix: "",
            suffix: "",
            range: [],
          },
        ],
      }),
    },
    tableColumns: {
      type: Array,
      default: () => [
        {
          prop: "no",
          label: "序号",
          minWidth: 60,
          align: "center",
          dateFormat: "yyyy-MM-dd HH:mm:ss",
        },
      ],
    },
    tableMaxHeight: {
      type: String,
      default: "calc(100vh - 200px)",
    },
    scrollInterval: {
      type: Number,
      default: commonConst.scrollInterval,
    },
    refreshInterval: {
      type: Number,
      default: commonConst.refreshInterval,
    },
    // 添加固定行数量配置
    fixedRowCount: {
      type: Number,
      default: 0,
    },
    // 行样式配置
    rowStyleConfig: {
      type: Object,
      default: () => ({}),
    },
    // 数据过滤配置
    dataFilterConfig: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      allData: [], // 存储接口返回的所有数据
      visibleData: [], // 界面展示的数据
      fixedData: [], // 固定显示的数据
      scrollableVisibleData: [], // 滚动显示的数据
      scrollTimer: null,
      refreshTimer: null,
      pageSize: 20, // 每页显示的条数
      currentPage: 0, // 当前页码
      currentMaxPageSize: 20, // 当前页码
      totalPages: -1, // 总页数
      total: -1, // 总条数
      isFetching: false, // 是否正在获取数据
      displayIndex: 0, // 当前显示数据的起始索引
      username: null,
      password: null,
    };
  },
  mounted() {
    // 从URL中获取查询参数
    this.username = this.$route.query.username || commonConst.username;
    this.password = this.$route.query.password || commonConst.password;
    this.pageSize = this.apiConfig.pageSize;
    this.fetchAllData();
  },
  beforeDestroy() {
    this.clearTimers();
  },
  methods: {
    async fetchAllData() {
      try {
        // 初始加载第一页数据
        await this.fetchMoreData(); // 确保等待fetchMoreData执行完成
        const initialLoadPages = Math.ceil(
          this.currentMaxPageSize / this.pageSize
        );
        for (let i = 0; i < initialLoadPages - 1; i++) {
          if (this.total !== -1 && this.total === this.allData.length) {
            break;
          }
          await this.fetchMoreData();
        }
        // 确保visibleData初始化
        this.updateVisibleData();
        this.startTimers();
        // 启动滚动定时器
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        this.isFetching = false;
      }
    },
    async fetchMoreData() {
      if (this.isFetching) return;
      this.isFetching = true;
      try {
        // 递增currentPage
        this.currentPage += 1;
        // 根据 isMock 决定使用模拟数据还是实际接口数据
        let response;
        if (this.apiConfig.isMock) {
          response = await this.simulateApiRequest(
            this.currentPage,
            this.pageSize
          );
        } else {
          response = await axios.post(this.apiConfig.url, {
            params: {
              page: this.currentPage,
              pageSize: this.pageSize,
            },
            username: this.username,
            password: this.password,
          });
        }
        let responseData =
          JSON.parse(JSON.stringify(response?.data?.data)) || {};
        let data = responseData.results || [];
        let total = responseData.total || 0;
        // 模拟数据时，动态生成序号
        for (let i = 0; i < data.length; i++) {
          data[i].no = (this.currentPage - 1) * this.pageSize + i + 1;
          // 为每个列应用自定义格式化
          this.tableColumns.forEach((column) => {
            // 如果列定义了dateFormat，则进行时间格式化
            if (column.dateFormat && data[i][column.prop]) {
              data[i][column.prop] = this.formatDateTime(
                data[i][column.prop],
                column.dateFormat
              );
            }
          });
        }

        // 应用数据过滤
        data = this.applyDataFilter(data);

        this.allData = [...this.allData, ...data];
        this.total = this.apiConfig.isMock
          ? this.apiConfig.mockDataTotal
          : total;
        this.totalPages = Math.ceil(this.total / this.pageSize);
      } catch (error) {
        console.error("获取更多数据失败:", error);
      } finally {
        this.isFetching = false;
      }
    },
    formatDateTime(dateString, format = "yyyy-MM-dd HH:mm:ss") {
      if (!dateString) return "";
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return dateString; // 如果日期无效则返回原始值

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0");
      const day = String(date.getDate()).padStart(2, "0");
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      return format
        .replace("yyyy", year)
        .replace("MM", month)
        .replace("dd", day)
        .replace("HH", hours)
        .replace("mm", minutes)
        .replace("ss", seconds);
    },
    simulateApiRequest(page, pageSize) {
      return new Promise((resolve) => {
        setTimeout(() => {
          const total = this.apiConfig.mockDataTotal;
          const data = Array.from({ length: pageSize }, (_, index) => {
            const item = { no: (page - 1) * pageSize + index + 1 };
            this.tableColumns.forEach((column) => {
              if (column.prop === "no") return;
              if (column.mockConfig) {
                switch (column.mockConfig.type) {
                  case "number":
                    item[column.prop] =
                      Math.floor(Math.random() * column.mockConfig.range[1]) +
                      column.mockConfig.range[0];
                    break;
                  case "string":
                    item[column.prop] =
                      column.mockConfig.prefix +
                      Math.floor(Math.random() * 1000) +
                      column.mockConfig.suffix;
                    break;
                  case "date":
                    item[column.prop] = "2025-10-10";
                    break;
                  case "time":
                    item[column.prop] = "2025-07-31 12:00:00";
                    break;
                  case "status":
                    item[column.prop] = ["合格", "不合格", "待检"][
                      Math.floor(Math.random() * 3)
                    ];
                    break;
                  default:
                    item[column.prop] = this.generateRandomChineseString(2, 5);
                }
              } else {
                // 默认生成随机中文字符串
                item[column.prop] = this.generateRandomChineseString(2, 5);
              }
            });
            return item;
          });
          resolve({ data, total });
        }, 50);
      });
    },
    generateRandomChineseString(minLength, maxLength) {
      const length =
        Math.floor(Math.random() * (maxLength - minLength + 1)) + minLength;
      let str = "";
      for (let i = 0; i < length; i++) {
        // 中文字符范围：0x4e00 到 0x9fa5
        const charCode =
          Math.floor(Math.random() * (0x9fa5 - 0x4e00 + 1)) + 0x4e00;
        str += String.fromCharCode(charCode);
      }
      return str;
    },
    scrollUpdateData() {
      // 如果数据量小于等于固定行数或没有滚动数据，则不滚动
      if (this.allData.length <= this.fixedRowCount) {
        return;
      }

      // 更新显示索引
      this.displayIndex += 1;

      // 检查是否需要加载更多数据
      const scrollableData = this.allData.slice(this.fixedRowCount);
      const visibleScrollableRows = this.pageSize - this.fixedRowCount;

      if (
        this.displayIndex + visibleScrollableRows * 2 >=
        scrollableData.length
      ) {
        if (this.currentPage + 1 <= this.totalPages) {
          this.fetchMoreData();
        } else {
          // 如果所有数据都已加载，则从头开始循环显示
          if (this.displayIndex === scrollableData.length) {
            this.displayIndex = 0;
          }
        }
      }
      this.updateVisibleData();
    },
    updateVisibleData() {
      // 固定部分数据
      this.fixedData = this.allData.slice(0, this.fixedRowCount);

      // 滚动部分数据
      const scrollableData = this.allData.slice(this.fixedRowCount);
      const visibleScrollableRows = this.pageSize - this.fixedRowCount;
      const end = this.displayIndex + visibleScrollableRows;

      if (this.total !== -1 && this.total < this.currentMaxPageSize) {
        this.scrollableVisibleData = [...scrollableData];
      } else if (end <= scrollableData.length) {
        this.scrollableVisibleData = scrollableData.slice(
          this.displayIndex,
          end
        );
      } else {
        const part1 = scrollableData.slice(this.displayIndex);
        const part2 = scrollableData.slice(0, end - scrollableData.length);
        this.scrollableVisibleData = [...part1, ...part2];
      }

      // 合并固定部分和滚动部分
      this.visibleData = [...this.fixedData, ...this.scrollableVisibleData];
    },
    startTimers() {
      this.clearTimers();
      this.scrollTimer = setInterval(() => {
        this.scrollUpdateData();
      }, this.scrollInterval);

      this.refreshTimer = setInterval(() => {
        this.refreshData();
      }, this.refreshInterval);
    },
    clearTimers() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null;
      }
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null;
      }
    },
    refreshData() {
      this.displayIndex = 0;
      this.currentPage = 0;
      this.allData = [];
      this.fetchAllData();
    },
    // 获取行样式类名
    getRowClassName({ row }) {
      if (!this.rowStyleConfig || !this.rowStyleConfig.dateField) {
        return "";
      }

      const { dateField, daysBefore = 3 } = this.rowStyleConfig;
      const dateValue = row[dateField];

      if (!dateValue) {
        return "";
      }

      // 解析日期
      const targetDate = new Date(dateValue);
      const currentDate = new Date();

      // 计算日期差（天数）
      const timeDiff = targetDate.getTime() - currentDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      // 如果目标日期在最近前3天内，返回红色样式类名
      if (daysDiff >= -daysBefore && daysDiff <= 0) {
        return "urgent-row";
      }

      // 如果状态为待关闭，返回绿色样式类名
      if (STARE.includes(row.state)) {
        return "greess-row";
      }

      return "";
    },
    // 应用数据过滤
    applyDataFilter(data) {
      if (!this.dataFilterConfig || !this.dataFilterConfig.field) {
        return data;
      }

      const { field, excludeValues = [] } = this.dataFilterConfig;

      return data.filter((row) => {
        const fieldValue = row[field];
        return !excludeValues.includes(fieldValue);
      });
    },
  },
};
</script>

<style scoped>
::v-deep .el-table {
  background-color: transparent !important;
  color: #fff !important;
  border-radius: 5px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #123d63 !important;
  color: #8ed1fc !important;
  border-bottom: 1px solid #1a4774 !important;
}

::v-deep .el-table td {
  border-bottom: 1px solid #1a4774 !important;
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #1a4774 !important;
  color: #fff;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #153e66 !important;
}

::v-deep .el-table__row {
  background-color: #133a5e !important;
  transition: all 0.5s ease;
}

::v-deep .el-table__row.el-table__row--striped {
  background-color: #153e66 !important;
}

.table-container {
  max-height: calc(100vh - 180px);
  overflow: hidden;
  padding: 15px;
  background-color: #133a5e;
  font-size: 24px;
}

::v-deep .el-table--scrollable-x .el-table__body-wrapper {
  overflow-x: hidden;
}

/* 紧急行样式 - 红色字体 */
::v-deep .el-table__row.urgent-row td {
  color: #ff4444 !important;
}
/* 待处理行样式 - 绿色字体 */
::v-deep .el-table__row.greess-row td {
  color: #00ff00 !important;
}
</style>
