<!--
 * @Author: 曾俊发 <EMAIL>
 * @Date: 2025-08-22 09:11:46
 * @LastEditors: 曾俊发 <EMAIL>
 * @LastEditTime: 2025-08-22 10:06:25
 * @FilePath: \odoo-report-forms\src\components\reportFormsSearch.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="reportFormsSearch">
    <el-form ref="form" :model="searchForm" label-width="120px">
      <el-row>
        <el-col
          v-for="item in formConfig"
          :key="item.prop"
          :span="item.span || 8"
        >
          <el-form-item :label="item.label">
            <el-input
              v-if="item.type === 'input'"
              v-model="searchForm[item.prop]"
            ></el-input>
            <el-select
              v-if="item.type === 'select'"
              style="width: 100%"
              v-model="searchForm[item.prop]"
              placeholder="请选择活动区域"
            >
              <el-option
                v-for="option in item.options"
                :key="option.optionCode"
                :label="option.optionName"
                :value="option.optionCode"
              ></el-option>
            </el-select>
            <el-date-picker
              v-if="item.type === 'date'"
              v-model="searchForm[item.prop]"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: "ReportFormsSearch",
  props: {
    formConfig: {
      type: Array,
      default: () => [
        {
          prop: "docNumberOrder",
          label: "服务订单类型",
          type: "input",
          span: 8,
        },
        {
          prop: "docTypeCodeOrder",
          label: "服务订单类型",
          type: "select",
          span: 8,
          options: [
            {
              optionCode: "1",
              optionName: "选项1",
            },
            {
              optionCode: "2",
              optionName: "选项2",
            },
            {
              optionCode: "3",
              optionName: "选项3",
            },
          ],
          multiple: true,
        },
        {
          prop: "docType",
          label: "服务订单类型",
          type: "date",
          span: 8,
        },
      ],
    },
  },
  data() {
    return {
      searchForm: {},
    };
  },
};
</script>

<style scoped lang="scss">
.reportFormsSearch {
  padding: 10px 20px;
  .el-form {
    .el-row {
      .el-col {
        .el-form-item {
          margin-bottom: 10px;
        }
      }
    }
  }
}
</style>
