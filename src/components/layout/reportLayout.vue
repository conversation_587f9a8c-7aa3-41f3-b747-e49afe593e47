<template>
  <div class="report-layout">
    <header class="report-header">
      <div class="company-info">
<!--        <img src="@/assets/images/logo.png" alt="公司logo">-->
        <h1>玉林市振来铸造有限公司</h1>
      </div>
      <h2>{{ reportTitle }}</h2>
      <div class="time-display">
        <span>{{ currentTime }}</span>
        <span>↻</span>
      </div>
    </header>
    <div class="report-body">
      <div class="report-content">
        <slot></slot> <!-- 报表具体内容将在这里显示 -->
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'reportLayout',

  props: {
    reportTitle: {
      type: String,
      required: true
    }
  },
  computed: {},
  created () {},
  data () {
    return {
      currentTime: new Date().toLocaleString(),
      updateTimeInterval: 1000 // 更新时间的频率
    }
  },
  methods: {
    updateCurrentTime () {
      this.currentTime = new Date().toLocaleString()
    }
  },
  mounted () {
    this.updateCurrentTime()
    this.timeInterval = setInterval(this.updateCurrentTime, this.updateTimeInterval)
  },
  beforeDestroy () {
    if (this.timeInterval) {
      clearInterval(this.timeInterval)
    }
  },
}
</script>


<style>
.report-layout {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  background-color: #0f2a42;
}

.report-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: #0a2445;
  border-bottom: 2px solid #0f3b63;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.company-info {
  display: flex;
  align-items: center;
}

.company-info img {
  width: 40px;
  height: 40px;
  margin-right: 10px;
}

.report-header h1 {
  font-size: 20px;
  margin: 0;
  color: #e8f4ff;
}

.report-header h2 {
  font-size: 24px;
  margin: 0;
  color: #fff;
  text-align: center;
  flex-grow: 1;
  font-weight: bold;
}

.time-display {
  display: flex;
  align-items: center;
}

.time-display span {
  margin-right: 10px;
  font-size: 14px;
  color: #fff;
}

.report-container {
  height: calc(100vh - 60px);
  overflow: hidden;
}

.report-content {
  height: 100%;
  overflow-y: auto;
  padding: 15px;
  background-color: #133a5e;
}
</style>
