export const STARE =['待关闭']

const commonConst = {
  scrollInterval: 3 * 1000,
  refreshInterval: 5 * 60 * 1000,
  username_dev: 'zlxgc',
  password_dev: 'Dmin123!@#qwe',
  baseUrl: '/vue-api',
  username: '',
  password: ''
}

// let devBaseUrl = 'http://localhost:8069'
// let prodBaseUrl = 'http://************:10161/'
// commonConst.baseUrl = process.env.NODE_ENV === 'dev' ? devBaseUrl : prodBaseUrl
// commonConst.baseUrl = process.env.NODE_ENV === 'development' ? devBaseUrl : "/vue-api"
commonConst.username = process.env.NODE_ENV === 'development' ? commonConst.username_dev : commonConst.username
commonConst.password = process.env.NODE_ENV === 'development' ? commonConst.password_dev : commonConst.password

console.log(process.env.NODE_ENV)

export default commonConst



