/*
 * @Author: 曾俊发 <EMAIL>
 * @Date: 2025-08-18 14:51:00
 * @LastEditors: 曾俊发 <EMAIL>
 * @LastEditTime: 2025-08-21 15:25:17
 * @FilePath: \odoo-report-forms\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from 'vue';
import VueRouter from 'vue-router';

Vue.use(VueRouter);

// 导入报表组件
import captionWorkshopManufacturingOrderProductionProgressTrackingDashboard from
    '../views/captionWorkshop/manufacturingOrderProductionProgressTrackingDashboard/index.vue';
import machiningWorkshopManufacturingOrderProductionProgressTrackingDashboard
  from '@/views/machiningWorkshop/manufacturingOrderProductionProgressTrackingDashboard/index.vue'
import weldingWorkshopManufacturingOrderProductionProgressTrackingDashboard
  from '@/views/weldingWorkshop/manufacturingOrderProductionProgressTrackingDashboard/index.vue'
import reportForms
  from '@/views/reportForms/index.vue'


export default new VueRouter({
  mode: 'hash', // 设置为 history 模式
  routes: [
    {
      path: '/captionWorkshop',
      name: 'captionWorkshopManufacturingOrderProductionProgressTrackingDashboard',
      component: captionWorkshopManufacturingOrderProductionProgressTrackingDashboard
    },
    {
      path: '/machiningWorkshop',
      name: 'machiningWorkshopManufacturingOrderProductionProgressTrackingDashboard',
      component: machiningWorkshopManufacturingOrderProductionProgressTrackingDashboard
    },
    {
      path: '/weldingWorkshop',
      name: 'weldingWorkshopManufacturingOrderProductionProgressTrackingDashboard',
      component: weldingWorkshopManufacturingOrderProductionProgressTrackingDashboard
    },
    {
      path: '/reportForms',
      name: 'reportForms',
      component: reportForms
    },
    {
      path: '/',
      redirect: '/captionWorkshop'
      // redirect: '/machiningWorkshop'
      // redirect: '/weldingWorkshop'
    }
  ]
});
