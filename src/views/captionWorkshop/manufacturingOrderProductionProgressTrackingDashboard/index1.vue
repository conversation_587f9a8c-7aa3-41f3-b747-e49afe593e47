<template>
  <report-layout report-title="铸造车间制造订单生产进度跟踪">
    <div class="table-container">
      <el-table
          ref="elTable"
          :data="visibleData"
          style="width: 100%"
          max-height="calc(100vh - 200px)"
      >
        <el-table-column prop="no" label="序号" min-width="60" align="center"></el-table-column>
        <el-table-column prop="partNumber" label="图号" min-width="120" align="center"></el-table-column>
        <el-table-column prop="name" label="名称" min-width="180" align="center"></el-table-column>
        <el-table-column prop="orderNumber" label="制造订单号" min-width="120" align="center"></el-table-column>
        <el-table-column prop="orderQuantity" label="制造订单数(件)" min-width="150" align="center"></el-table-column>
        <el-table-column prop="planCompletionDate" label="订单计划入库时间" min-width="180" align="center"></el-table-column>
        <el-table-column prop="moldingTime" label="造型时间" min-width="150" align="center"></el-table-column>
        <el-table-column prop="moldQuantity" label="造型数量(件)" min-width="120" align="center"></el-table-column>
        <el-table-column prop="totalMoldQuantity" label="毛胚入库数量(件)" min-width="150" align="center"></el-table-column>
        <el-table-column prop="completionRate" label="进度完成率" min-width="100" align="center"></el-table-column>
        <el-table-column prop="qualityStatus" label="质量状态" min-width="100" align="center"></el-table-column>
      </el-table>
    </div>
  </report-layout>
</template>

<script>
import ReportLayout from '@/components/layout/reportLayout.vue';

export default {
  name: 'captionWorkshopManufacturingOrderProductionProgressTrackingDashboard',
  components: {
    ReportLayout
  },
  data() {
    return {
      allData: [], // 存储接口返回的所有数据
      visibleData: [], // 界面展示的数据
      scrollInterval: 500, // 滚动刷新频率（1秒）
      refreshInterval: 60000, // 刷新频率（1分钟）
      scrollTimer: null,
      refreshTimer: null,
      pageSize: 20,   // 每页显示的条数（每次更新一行）
      currentPage: 0, // 当前页码（初始化为0）
      currentMaxPageSize: 20, // 当前页码（初始化为0）
      totalPages: -1,  // 总页数
      mockDataTotal: 100, // 模拟数据总数
      total: 0,       // 总条数
      isFetching: false, // 是否正在获取数据
      displayIndex: 0  // 当前显示数据的起始索引
    };
  },
  mounted() {
    this.fetchAllData();
  },
  beforeDestroy() {
    this.clearTimers();
  },
  methods: {
    async fetchAllData() {
      try {
        // 初始加载第一页数据
        await this.fetchMoreData(); // 确保等待fetchMoreData执行完成
        while (this.allData.length < this.currentMaxPageSize && this.allData.length !== this.total){
          await this.fetchMoreData();
        }
        // 确保visibleData初始化
        this.updateVisibleData();
        // 启动滚动定时器
        this.startTimers();
      } catch (error) {
        console.error('获取数据失败:', error);
      } finally {
        this.isFetching = false;
        console.log('数据加载结束'); // 新增日志
      }
    },
    async fetchMoreData() {
      if (this.isFetching) return;
      this.isFetching = true;
      try {
        // 递增currentPage
        this.currentPage += 1;

        // 模拟从后端获取分页数据
        const response = await this.simulateApiRequest(this.currentPage, this.pageSize);
        this.allData = [...this.allData, ...response.data];
        this.total = response.total;
        this.totalPages = Math.ceil(this.total / this.pageSize);
      } catch (error) {
        console.error('获取更多数据失败:', error);
      } finally {
        this.isFetching = false;
      }
    },
    simulateApiRequest(page, pageSize) {
      return new Promise(resolve => {
        setTimeout(() => {
          const total = this.mockDataTotal;
          const data = Array.from({ length: pageSize }, (_, index) => ({
            no: (page - 1) * pageSize + index + 1,
            partNumber: "MM200-" + Math.floor(Math.random() * 1000),
            name: "ECU支撑板组件",
            orderNumber: "12456",
            orderQuantity: Math.floor(Math.random() * 900) + 100,
            planCompletionDate: "2025-10-10",
            moldingTime: "2025-07-31 12:00:00",
            moldQuantity: Math.floor(Math.random() * 40) + 10,
            totalMoldQuantity: Math.floor(Math.random() * 40) + 10,
            completionRate: Math.floor(Math.random() * 100) + 1 + "%",
            qualityStatus: ["合格", "不合格", "待检"][Math.floor(Math.random() * 3)]
          }));

          resolve({ data, total });
        }, 50);
      });
    },
    scrollUpdateData() {
      if (this.allData.length === 0) {
        return;
      }
      // 更新显示索引
      this.displayIndex += 1;

      // 检查是否需要加载更多数据
      if (this.displayIndex + this.pageSize * 2 >= this.allData.length ) {
        if (this.currentPage + 1 <= this.totalPages) {
          this.fetchMoreData();
        } else {
          // 如果所有数据都已加载，则从头开始循环显示
          if (this.displayIndex === this.total){
            this.displayIndex = 0
          }
        }
      }
      // 更新展示数据
      this.updateVisibleData();
    },
    updateVisibleData() {
      const end = this.displayIndex + this.pageSize;
      if (end <= this.allData.length) {
        this.visibleData = [...this.allData.slice(this.displayIndex, end)];
      } else {
        // 如果索引超出数组长度，从头开始补足
        const part1 = this.allData.slice(this.displayIndex);
        const part2 = this.allData.slice(0, end - this.allData.length);
        this.visibleData = [...part1, ...part2];
      }
    },
    startTimers() {
      // 清理之前的定时器（如果存在）
      if (this.scrollTimer) clearInterval(this.scrollTimer);
      if (this.refreshTimer) clearInterval(this.refreshTimer);
      this.scrollTimer = setInterval(() => {
        this.scrollUpdateData();
      }, this.scrollInterval);
      this.refreshTimer = setInterval(() => {
        this.refreshData();
      }, this.refreshInterval);
    },
    clearTimers() {
      if (this.scrollTimer) {
        clearInterval(this.scrollTimer);
        this.scrollTimer = null; // 确保定时器被置为 null
      }
      if (this.refreshTimer) {
        clearInterval(this.refreshTimer);
        this.refreshTimer = null; // 确保定时器被置为 null
      }
    },
    refreshData() {
      console.log('开始刷新数据'); // 新增日志
      this.displayIndex = 0;
      this.currentPage = 0;
      this.allData = [];
      this.fetchAllData();
    }
  }
}
</script>

<style scoped>
::v-deep .el-table {
  background-color: transparent !important; /* 添加 !important */
  color: #fff !important; /* 添加 !important */
  border-radius: 5px;
  overflow: hidden;
}

::v-deep .el-table th {
  background-color: #123d63 !important; /* 添加 !important */
  color: #8ed1fc !important; /* 添加 !important */
  border-bottom: 1px solid #1a4774 !important; /* 添加 !important */
}

::v-deep .el-table td {
  border-bottom: 1px solid #1a4774 !important; /* 添加 !important */
}

::v-deep .el-table--enable-row-hover .el-table__body tr:hover > td {
  background-color: #1a4774 !important;
  color: #fff;
}

::v-deep .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: #153e66 !important;
}

::v-deep .el-table__row {
  background-color: #133a5e !important;
  transition: all 0.5s ease;
}

::v-deep .el-table__row.el-table__row--striped {
  background-color: #153e66 !important;
}

.table-container {
  max-height: calc(100vh - 180px);
  overflow: hidden;
  padding: 15px;
  background-color: #133a5e;
}
</style>
