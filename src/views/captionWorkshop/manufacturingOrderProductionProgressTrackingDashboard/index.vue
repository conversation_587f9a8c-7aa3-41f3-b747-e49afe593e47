<template>
  <div>
    <general-dashboard
        report-title="铸造车间制造订单生产进度"
        :api-config="{ url: apiUrl, pageSize: 20, isMock: false }"
        :table-columns="tableColumns"
        table-max-height="calc(100vh - 200px)"
        :row-style-config="rowStyleConfig"
    />
  </div>
</template>

<script>
import GeneralDashboard from '@/components/generalDashboard/index.vue'
import commonConst from '@/api/common/commonConst'

export default {
  name: 'captionWorkshopManufacturingOrderProductionProgressTrackingDashboard',
  components: { GeneralDashboard },
  data () {
    return {
      apiUrl: commonConst.baseUrl + '/yuchai_api/vue_report/zhenlai_vue_report_caption_workshop',
      tableColumns: [
        { prop: 'no', label: '序号', minWidth: 60, align: 'center' },
        { prop: 'manufacturing_order_number', label: '制造订单号', minWidth: 120, align: 'center' },
        { prop: 'figure_number', label: '图号', minWidth: 130, align: 'center' },
        { prop: 'figure_number_name', label: '产品名称', minWidth: 180, align: 'center' },
        { prop: 'planned_production_quantity', label: '计划生产数量', minWidth: 120, align: 'center' },
        { prop: 'planned_stock_in_time', label: '订单计划入库时间', minWidth: 120, align: 'center' },
        { prop: 'first_operation_time', label: '造型时间', minWidth: 120, align: 'center' },
        { prop: 'first_operation_quantity', label: '造型数量', minWidth: 120, align: 'center' },
        { prop: 'mprk_quantity', label: '毛坯入库数量', minWidth: 120, align: 'center' },
        { prop: 'completion_rate_percent', label: '进度完成率', minWidth: 120, align: 'center' },
        { prop: 'quality_status_identification', label: '质量标识状态', minWidth: 120, align: 'center' },
      ],
      // 行样式配置 - 订单要求入库日期最近前3天的订单显示红色
      rowStyleConfig: {
        dateField: 'planned_stock_in_time', // 订单计划入库时间字段
        daysBefore: 3 // 最近前3天
      }
    }
  },
  computed: {
    commonConst () {
      return commonConst
    }
  },
  created () {},
  methods: {}
}
</script>


<style scoped>

</style>

