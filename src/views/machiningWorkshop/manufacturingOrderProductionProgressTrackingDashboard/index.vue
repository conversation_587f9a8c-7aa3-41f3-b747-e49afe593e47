<template>
  <div>
    <general-dashboard
        report-title="机加工车间制造订单生产进度"
        :api-config="{ url: apiUrl, pageSize: 20, isMock: false }"
        :table-columns="tableColumns"
        table-max-height="calc(100vh - 200px)"
    />
  </div>
</template>

<script>
import GeneralDashboard from '@/components/generalDashboard/index.vue'
import commonConst from '@/api/common/commonConst'

export default {
  name: 'machiningWorkshopManufacturingOrderProductionProgressTrackingDashboard',
  components: { GeneralDashboard },
  data () {
    return {
      apiUrl: commonConst.baseUrl + '/yuchai_api/vue_report/zhenlai_vue_report_machining_workshop',
      tableColumns: [
        { prop: 'no', label: '序号', minWidth: 60, align: 'center' },
        { prop: 'manufacturing_order_number', label: '制造订单号', minWidth: 120, align: 'center' },
        { prop: 'figure_number', label: '图号', minWidth: 130, align: 'center' },
        { prop: 'figure_number_name', label: '产品名称', minWidth: 180, align: 'center' },
        { prop: 'planned_production_quantity', label: '计划生产数量', minWidth: 120, align: 'center' },
        { prop: 'planned_stock_in_time', label: '要求入库日期', minWidth: 120, align: 'center' },
        { prop: 'first_operation_time', label: '第一道工序报工时间', minWidth: 120, align: 'center' },
        { prop: 'first_operation_quantity', label: '第一道工序报工数量', minWidth: 120, align: 'center' },
        { prop: 'zkgy_quantity', label: '钻孔攻牙工序完成数', minWidth: 120, align: 'center' },
        { prop: 'dph_quantity', label: '动平衡工序完成数', minWidth: 120, align: 'center' },
        { prop: 'dmqpf_quantity', label: '打磨去披锋工序完成(件)', minWidth: 120, align: 'center' },
        { prop: 'last_operation_qualified_qty', label: '已生产数量', minWidth: 120, align: 'center' },
        { prop: 'completion_rate_percent', label: '进度完成率', minWidth: 120, align: 'center' },
        { prop: 'quality_status_identification', label: '质量标识状态', minWidth: 120, align: 'center' },
      ]
    }
  },
  computed: {
    commonConst () {
      return commonConst
    }
  },
  created () {},
  methods: {}
}
</script>


<style scoped>

</style>
