<template>
  <div>
    <general-dashboard
        report-title="焊接车间制造订单生产进度"
        :api-config="{ url: apiUrl, pageSize: 20, isMock: false }"
        :table-columns="tableColumns"
        table-max-height="calc(100vh - 200px)"
    />
  </div>
</template>

<script>
import GeneralDashboard from '@/components/generalDashboard/index.vue'
import commonConst from '@/api/common/commonConst'

export default {
  name: 'weldingWorkshopManufacturingOrderProductionProgressTrackingDashboard',
  components: { GeneralDashboard },
  data () {
    return {
      apiUrl: commonConst.baseUrl + '/yuchai_api/vue_report/zhenlai_vue_report_welding_workshop',
      tableColumns: [
        { prop: 'no', label: '序号', minWidth: 60, align: 'center' },
        { prop: 'manufacturing_order_number', label: '制造订单号', minWidth: 120, align: 'center' },
        { prop: 'figure_number', label: '图号', minWidth: 150, align: 'center' },
        { prop: 'figure_number_name', label: '产品名称', minWidth: 180, align: 'center' },
        { prop: 'planned_stock_in_time', label: '要求入库日期', minWidth: 180, align: 'center', dateFormat: 'yyyy-MM-dd'},
        { prop: 'first_material_requisition_time', label: '首道工序合格数', minWidth: 150, align: 'center' , dateFormat: 'yyyy-MM-dd'},
        { prop: 'planned_production_quantity', label: '计划生产数量', minWidth: 150, align: 'center' },
        { prop: 'marking_number', label: '打标识', minWidth: 150, align: 'center' },
        { prop: 'completed_quantity', label: '已生产数量', minWidth: 150, align: 'center' },
        { prop: 'completion_rate_percent', label: '进度完成率', minWidth: 100, align: 'center' },
        { prop: 'qualityStatus', label: '质量状态标识', minWidth: 100, align: 'center' }
      ]
    }
  },
  computed: {
    commonConst () {
      return commonConst
    }
  },
  created () {},
  methods: {}
}
</script>


<style scoped>

</style>
