<!--
 * @Author: 曾俊发 <EMAIL>
 * @Date: 2025-08-21 15:12:20
 * @LastEditors: 曾俊发 <EMAIL>
 * @LastEditTime: 2025-08-22 10:04:48
 * @FilePath: \odoo-report-forms\src\views\reportForms\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="reportForms">
    <ReportFormsSearch></ReportFormsSearch>
    <div class="reportFormsTable">
      <el-table height="550" :data="tableData" border style="width: 100%">
        <el-table-column
          v-for="column in columns"
          :key="column.prop"
          :prop="column.prop"
          :label="column.label"
          :width="column.minWidth"
          :align="column.align"
          :show-overflow-tooltip="column.showOverflowTooltip"
        ></el-table-column>
      </el-table>
    </div>
    <div class="pagination">
      <el-pagination
        background
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[100, 200, 300, 400]"
        :page-size="100"
        layout="total, sizes, prev, pager, next, jumper"
        :total="400"
      >
      </el-pagination>
    </div>
  </div>
</template>

<script>
import ReportFormsSearch from "@/components/reportFormsSearch.vue";
export default {
  name: "ReportForms",
  components: {
    ReportFormsSearch,
  },
  data() {
    return {
      form: {
        name: "",
        region: "",
        date1: [],
        date2: "",
        delivery: false,
        type: [],
        resource: "",
        desc: "",
      },
      columns: [
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "company",
          label: "授权公司",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "officeCode",
          label: "驻外区域编码",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "officeDp",
          label: "驻外区域名称",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "docNumber",
          label: "申请单号",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "stationName",
          label: "服务站名称",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "num",
          resizable: true,
          showOverflowTooltip: true,
          prop: "businessQuantity",
          label: "辖区玉柴业务量",
          align: "right",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "nationCode",
          label: "国家代码",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "nation",
          label: "国家",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "province",
          label: "省",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "city",
          label: "市",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "counties",
          label: "县",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "detailedAddress",
          label: "详细地址",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "stationSummarize",
          label: "单位简要概况",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "applyPurpose",
          label: "建站目的和作用",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "stationARename",
          label: "原A站号",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "enterpriseNature",
          label: "企业性质",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "num",
          resizable: true,
          showOverflowTooltip: true,
          prop: "productsQuantity",
          label: "辖区玉柴产品保有量",
          align: "right",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "num",
          resizable: true,
          showOverflowTooltip: true,
          prop: "customerQuantity",
          label: "服务站客户量",
          align: "right",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "mergePlantDp",
          label: "提出并轨主机厂",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "maintenanceQualification",
          label: "维修资质",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "dealerFlag",
          label: "是否经销商",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "dealerName",
          label: "经销商名称",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "proxyBrandDp",
          label: "代理品牌名称",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "proxyBrandCode",
          label: "代理品牌编码",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "plate",
          label: "plate",
          align: "center",
          minWidth: "150px",
          isShow: false,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "vehicleFactory",
          label: "其他厂家授权-整车厂",
          align: "center",
          width: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "engineFactory",
          label: "其他厂家授权-发动机厂",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "factoryDp",
          label: "factoryDp",
          align: "center",
          minWidth: "150px",
          isShow: false,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "recentStationShort",
          label: "最近服务站简称",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "num",
          resizable: true,
          showOverflowTooltip: true,
          prop: "recentStationDistance",
          label: "最近的服务站距离（公里）",
          align: "right",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "recentCoreStation",
          label: "最近的核心站",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "num",
          resizable: true,
          showOverflowTooltip: true,
          prop: "recentCoreStationDistance",
          label: "最近的核心站距离（公里）",
          align: "right",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "applyType",
          label: "申请建站类型",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "businessSegmentApply",
          label: "拟申请业务板块",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "applyReason",
          label: "建站缘由",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "inspectorOpinion",
          label: "考察人意见",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "officeOpinion",
          label: "服务经理意见",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "directorOpinion",
          label: "办事处主任意见",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "str",
          resizable: true,
          showOverflowTooltip: true,
          prop: "docType",
          label: "当前环节",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
        {
          type: "date",
          resizable: true,
          showOverflowTooltip: true,
          prop: "creationDate",
          label: "创建时间",
          align: "center",
          minWidth: "150px",
          isShow: true,
          fixedType: false,
        },
      ],
      tableData: [
        {
          company: "2016-05-02",
          officeCode: "王小虎",
          officeDp: "上海市普陀区金沙江路 1518 弄",
        },
      ],
    };
  },
  methods: {
    handleSizeChange(val) {
      console.log(`每页 ${val} 条`);
      
    },
    handleCurrentChange(val) {
      console.log(`当前页: ${val}`);
    },
  },
};
</script>

<style scoped lang="scss">
.reportForms {
  width: 100%;
  .reportFormsTable {
    padding: 10px 20px;
  }
  .pagination {
    text-align: right;
    margin-right: 20px;
  }
}
</style>
<style lang="scss">
.reportFormsTable {
  .el-table th.el-table__cell > .cell {
    white-space: nowrap; /* 防止内容换行 */
  }
  .el-table th.el-table__cell {
    background-color: skyblue;
    padding: 0px;
    color: #333;
  }
  .el-table .el-table__cell {
    padding: 5px 0px;
  }
}
</style>
